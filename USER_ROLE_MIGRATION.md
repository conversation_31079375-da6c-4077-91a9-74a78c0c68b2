# User Role Unification Migration Plan

## Overview

This document outlines the migration strategy to consolidate the current 3 separate user tables (`user`, `admin`, `mentor`) plus the addition of a new `agent` user type into a single unified user table with role-based access control. This migration will simplify user management, improve security, and enable more flexible role assignments including support for immigration agents.

## Current State Analysis

### Existing Tables (3 Tables + 1 New Role)

#### User Table
```sql
CREATE TABLE "user" (
    "id" TEXT PRIMARY KEY,
    "name" TEXT NOT NULL,
    "email" TEXT UNIQUE NOT NULL,
    "emailVerified" BOOLEAN DEFAULT false,
    "image" TEXT,
    "password" TEXT,
    "provider" "Provider" NOT NULL,  -- 'credentials' | 'google' | 'facebook'
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3)
);
```

#### Admin Table
```sql
CREATE TABLE "admin" (
    "id" TEXT PRIMARY KEY,
    "name" TEXT NOT NULL,
    "email" TEXT UNIQUE NOT NULL,
    "emailVerified" BOOLEAN DEFAULT false,
    "image" TEXT,
    "password" TEXT,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3)
);
```

#### Mentor Table
```sql
CREATE TABLE "mentor" (
    "id" TEXT PRIMARY KEY,
    "name" TEXT NOT NULL,
    "email" TEXT UNIQUE NOT NULL,
    "emailVerified" BOOLEAN DEFAULT false,
    "image" TEXT,
    "password" TEXT,
    "location" TEXT,
    "designation" TEXT NOT NULL,
    "desc" TEXT NOT NULL,
    "order" INTEGER,
    "linkedin" TEXT,
    "profile" TEXT,
    "status" "Status" DEFAULT 'Active',
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),
    "servicesId" TEXT
);
```

#### Agent Table (New Role - No Existing Table)
```sql
-- Agent users will be created directly in the unified table
-- Agent-specific fields will be part of the unified schema
-- Agents are immigration consultants/lawyers who provide immigration services
```

### Common Fields Analysis
All three tables share these core fields:
- `id`, `name`, `email`, `emailVerified`, `image`, `password`, `createdAt`, `updatedAt`

### Unique Fields Analysis
- **User**: `provider` (OAuth provider information)
- **Admin**: No unique fields
- **Mentor**: `location`, `designation`, `desc`, `order`, `linkedin`, `profile`, `status`, `servicesId`
- **Agent**: `license_number`, `specializations`, `years_experience`, `consultation_fee`, `languages`, `office_address`, `certification_body`, `license_expiry`

### Current Authentication System
- **3 separate JWT secrets**: `jwtSecretKey`, `jwtAdminSecretKey`, `jwtMentorSecretKey`
- **3 separate guards**: `JwtGuard`, `JwtAdmin`, `JwtMentor`
- **3 separate login endpoints**: `/user/login`, `/admin/login`, `/mentor/login`
- **Agent authentication**: Will be implemented as part of unified system

## Target State Design

### Unified User Table Schema

```sql
CREATE TABLE "unified_user" (
    -- Core user fields
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "emailVerified" BOOLEAN DEFAULT false,
    "image" TEXT,
    "password" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    -- Role and authentication
    "role" TEXT NOT NULL DEFAULT 'user', -- 'user', 'admin', 'mentor', 'agent'
    "status" TEXT NOT NULL DEFAULT 'active', -- 'active', 'inactive', 'pending', 'suspended'
    "provider" TEXT DEFAULT 'credentials', -- 'credentials', 'google', 'facebook'

    -- Mentor-specific fields (nullable for non-mentors)
    "location" TEXT,
    "designation" TEXT,
    "description" TEXT,
    "display_order" INTEGER,
    "linkedin_url" TEXT,
    "profile_url" TEXT,

    -- Agent-specific fields (nullable for non-agents)
    "license_number" TEXT,
    "specializations" JSONB, -- Array of immigration specializations
    "years_experience" INTEGER,
    "consultation_fee" INTEGER, -- Fee in cents
    "languages" JSONB, -- Array of languages spoken
    "office_address" TEXT,
    "certification_body" TEXT, -- e.g., "ICCRC", "Law Society of Ireland"
    "license_expiry" DATE,

    -- Permissions and metadata
    "permissions" JSONB DEFAULT '[]', -- Array of specific permissions
    "metadata" JSONB DEFAULT '{}', -- Additional role-specific data
    "last_login" TIMESTAMP(3),
    "login_count" INTEGER DEFAULT 0,

    CONSTRAINT "unified_user_pkey" PRIMARY KEY ("id")
);

-- Indexes for performance
CREATE UNIQUE INDEX "unified_user_email_key" ON "unified_user"("email");
CREATE INDEX "unified_user_role_idx" ON "unified_user"("role");
CREATE INDEX "unified_user_status_idx" ON "unified_user"("status");
CREATE INDEX "unified_user_provider_idx" ON "unified_user"("provider");
CREATE INDEX "unified_user_last_login_idx" ON "unified_user"("last_login");

-- Check constraints for data integrity
ALTER TABLE "unified_user" ADD CONSTRAINT "unified_user_role_check"
    CHECK (role IN ('user', 'admin', 'mentor', 'agent', 'super_admin'));

ALTER TABLE "unified_user" ADD CONSTRAINT "unified_user_status_check"
    CHECK (status IN ('active', 'inactive', 'pending', 'suspended', 'deleted'));

ALTER TABLE "unified_user" ADD CONSTRAINT "unified_user_provider_check"
    CHECK (provider IN ('credentials', 'google', 'facebook', 'linkedin'));

-- Mentor-specific constraints
ALTER TABLE "unified_user" ADD CONSTRAINT "unified_user_mentor_fields_check"
    CHECK (
        (role = 'mentor' AND designation IS NOT NULL AND description IS NOT NULL) OR
        (role != 'mentor')
    );

-- Agent-specific constraints
ALTER TABLE "unified_user" ADD CONSTRAINT "unified_user_agent_fields_check"
    CHECK (
        (role = 'agent' AND license_number IS NOT NULL AND certification_body IS NOT NULL) OR
        (role != 'agent')
    );
```

### Role-Based Permissions System

```typescript
enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  MENTOR = 'mentor',
  AGENT = 'agent',
  SUPER_ADMIN = 'super_admin'
}

enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
  DELETED = 'deleted'
}

interface UserPermissions {
  // Admin permissions
  manage_users?: boolean;
  manage_mentors?: boolean;
  manage_services?: boolean;
  manage_payments?: boolean;
  view_analytics?: boolean;

  // Mentor permissions
  manage_own_services?: boolean;
  view_own_analytics?: boolean;
  respond_to_reviews?: boolean;

  // Agent permissions
  manage_immigration_services?: boolean;
  view_client_cases?: boolean;
  process_applications?: boolean;
  generate_reports?: boolean;
  manage_consultations?: boolean;

  // User permissions
  purchase_services?: boolean;
  leave_reviews?: boolean;
  access_dashboard?: boolean;
}
```

## Migration Benefits

### 1. **Simplified User Management**
- **Before**: 3 separate user management systems + new agent system needed
- **After**: Single unified user management with role-based access for all 4 user types
- **Impact**: 75% reduction in user management complexity (4 systems → 1)

### 2. **Enhanced Security**
- **Single JWT Secret**: Unified authentication system
- **Role-Based Permissions**: Granular permission control
- **Audit Trail**: Centralized user activity tracking

### 3. **Improved Scalability**
- **New Roles**: Easy to add new user types (e.g., moderator, support, legal_advisor)
- **Permission Management**: Flexible permission assignment
- **Multi-Role Support**: Users can have multiple roles (e.g., mentor + agent)

### 4. **Better User Experience**
- **Single Login**: One login system for all user types
- **Role Switching**: Users can switch between roles if they have multiple
- **Unified Profile**: Single profile management interface

## Migration Strategy

### Phase 1: Preparation (Zero Downtime)

#### 1.1 Create Unified User Table
```sql
-- Create the new unified user table alongside existing tables
CREATE TABLE "unified_user" (
    -- Schema as defined above
);
```

#### 1.2 Update Prisma Schema
```prisma
model unified_user {
  id              String               @id @default(cuid())
  name            String
  email           String               @unique
  emailVerified   Boolean              @default(false)
  image           String?
  password        String?
  role            UserRole             @default(USER)
  status          UserStatus           @default(ACTIVE)
  provider        AuthProvider         @default(CREDENTIALS)

  // Mentor-specific fields
  location        String?
  designation     String?
  description     String?
  display_order   Int?
  linkedin_url    String?
  profile_url     String?

  // Metadata and tracking
  permissions     Json                 @default("[]")
  metadata        Json                 @default("{}")
  last_login      DateTime?
  login_count     Int                  @default(0)

  createdAt       DateTime             @default(now())
  updatedAt       DateTime             @updatedAt

  // Relationships
  reviews_given   review[]             @relation("UserReviews")
  reviews_received review[]            @relation("MentorReviews")
  services        service[]            @relation("MentorServices")
  payments        payment[]            @relation("UserPayments")
  comments        comment[]

  @@index([role])
  @@index([status])
  @@index([provider])
  @@index([last_login])
}

enum UserRole {
  USER
  ADMIN
  MENTOR
  SUPER_ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
  DELETED
}

enum AuthProvider {
  CREDENTIALS
  GOOGLE
  FACEBOOK
  LINKEDIN
}
```

#### 1.3 Create Migration Scripts
```typescript
// scripts/migrate-users.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migrateUsers() {
  console.log('Starting user migration...');

  // Migrate regular users
  await migrateRegularUsers();

  // Migrate admin users
  await migrateAdminUsers();

  // Migrate mentor users
  await migrateMentorUsers();

  // Create sample agent users (no existing table to migrate from)
  await createSampleAgentUsers();

  console.log('User migration completed!');
}

async function migrateRegularUsers() {
  const users = await prisma.user.findMany();

  for (const user of users) {
    await prisma.unified_user.create({
      data: {
        id: user.id,
        name: user.name,
        email: user.email,
        emailVerified: user.emailVerified || false,
        image: user.image,
        password: user.password,
        role: 'USER',
        status: 'ACTIVE',
        provider: user.provider?.toUpperCase() || 'CREDENTIALS',
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        permissions: ['purchase_services', 'leave_reviews', 'access_dashboard'],
        metadata: {
          migrated_from: 'user_table',
          original_provider: user.provider,
        },
      },
    });
  }

  console.log(`Migrated ${users.length} regular users`);
}

async function migrateAdminUsers() {
  const admins = await prisma.admin.findMany();

  for (const admin of admins) {
    await prisma.unified_user.create({
      data: {
        id: admin.id,
        name: admin.name,
        email: admin.email,
        emailVerified: admin.emailVerified || false,
        image: admin.image,
        password: admin.password,
        role: 'ADMIN',
        status: 'ACTIVE',
        provider: 'CREDENTIALS',
        createdAt: admin.createdAt,
        updatedAt: admin.updatedAt,
        permissions: [
          'manage_users',
          'manage_mentors',
          'manage_services',
          'manage_payments',
          'view_analytics'
        ],
        metadata: {
          migrated_from: 'admin_table',
        },
      },
    });
  }

  console.log(`Migrated ${admins.length} admin users`);
}

async function migrateMentorUsers() {
  const mentors = await prisma.mentor.findMany();

  for (const mentor of mentors) {
    await prisma.unified_user.create({
      data: {
        id: mentor.id,
        name: mentor.name,
        email: mentor.email,
        emailVerified: mentor.emailVerified || false,
        image: mentor.image,
        password: mentor.password,
        role: 'MENTOR',
        status: mentor.status?.toUpperCase() || 'ACTIVE',
        provider: 'CREDENTIALS',
        location: mentor.location,
        designation: mentor.designation,
        description: mentor.desc,
        display_order: mentor.order,
        linkedin_url: mentor.linkedin,
        profile_url: mentor.profile,
        createdAt: mentor.createdAt,
        updatedAt: mentor.updatedAt,
        permissions: [
          'manage_own_services',
          'view_own_analytics',
          'respond_to_reviews'
        ],
        metadata: {
          migrated_from: 'mentor_table',
          services_id: mentor.servicesId,
        },
      },
    });
  }

  console.log(`Migrated ${mentors.length} mentor users`);
}

async function createSampleAgentUsers() {
  console.log('Creating sample agent users...');

  const sampleAgents = [
    {
      name: 'Sarah O\'Connor',
      email: '<EMAIL>',
      license_number: 'ICCRC-2024-001',
      specializations: ['Family Reunification', 'Work Permits', 'Student Visas'],
      years_experience: 8,
      consultation_fee: 15000, // €150 in cents
      languages: ['English', 'Irish', 'French'],
      office_address: 'Dublin 2, Ireland',
      certification_body: 'ICCRC',
      license_expiry: '2025-12-31',
    },
    {
      name: 'Michael Chen',
      email: '<EMAIL>',
      license_number: 'LSI-2024-002',
      specializations: ['Business Immigration', 'Investment Visas', 'EU Blue Card'],
      years_experience: 12,
      consultation_fee: 20000, // €200 in cents
      languages: ['English', 'Mandarin', 'Cantonese'],
      office_address: 'Cork, Ireland',
      certification_body: 'Law Society of Ireland',
      license_expiry: '2026-06-30',
    },
  ];

  for (const agent of sampleAgents) {
    await prisma.unified_user.create({
      data: {
        name: agent.name,
        email: agent.email,
        emailVerified: true,
        password: await hash('TempPassword123!', 10), // Temporary password
        role: 'AGENT',
        status: 'ACTIVE',
        provider: 'credentials',
        license_number: agent.license_number,
        specializations: agent.specializations,
        years_experience: agent.years_experience,
        consultation_fee: agent.consultation_fee,
        languages: agent.languages,
        office_address: agent.office_address,
        certification_body: agent.certification_body,
        license_expiry: new Date(agent.license_expiry),
        permissions: [
          'manage_immigration_services',
          'view_client_cases',
          'process_applications',
          'generate_reports',
          'manage_consultations',
          'manage_profile'
        ],
        metadata: {
          created_during_migration: true,
          user_type: 'sample_agent',
          creation_date: new Date().toISOString(),
        },
        login_count: 0,
      },
    });
  }

  console.log(`Created ${sampleAgents.length} sample agent users`);
}
```

### Phase 2: Unified Authentication System

#### 2.1 Create Unified Auth Service
```typescript
// src/auth/unified-auth.service.ts
@Injectable()
export class UnifiedAuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
  ) {}

  async login(dto: LoginDto) {
    const user = await this.validateUser(dto);

    const payload = {
      id: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      sub: {
        name: user.name,
      },
    };

    if (!user.emailVerified && user.role !== 'ADMIN') {
      throw new UnauthorizedException('Email not verified');
    }

    // Update login tracking
    await this.updateLoginTracking(user.id);

    return {
      user: this.sanitizeUser(user),
      backendTokens: {
        accessToken: await this.jwtService.signAsync(payload, {
          expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE,
          secret: process.env.JWT_SECRET_KEY, // Single secret
        }),
        refreshToken: await this.jwtService.signAsync(payload, {
          expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE,
          secret: process.env.JWT_REFRESH_SECRET_KEY,
        }),
        expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
      },
    };
  }

  async validateUser(dto: LoginDto) {
    const user = await this.prisma.unified_user.findUnique({
      where: { email: dto.email },
    });

    if (!user || user.status === 'DELETED') {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (user.status === 'SUSPENDED') {
      throw new UnauthorizedException('Account suspended');
    }

    if (user.password && (await compare(dto.password, user.password))) {
      return user;
    }

    throw new UnauthorizedException('Invalid credentials');
  }

  private async updateLoginTracking(userId: string) {
    await this.prisma.unified_user.update({
      where: { id: userId },
      data: {
        last_login: new Date(),
        login_count: { increment: 1 },
      },
    });
  }

  private sanitizeUser(user: any) {
    const { password, ...sanitized } = user;
    return sanitized;
  }
}
```

#### 2.2 Create Unified Auth Guard
```typescript
// src/guards/unified-auth.guard.ts
@Injectable()
export class UnifiedAuthGuard implements CanActivate {
  constructor(private jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('No bearer token');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET_KEY,
      });

      request['user'] = payload;
      return true;
    } catch {
      throw new UnauthorizedException('Invalid token');
    }
  }

  private extractTokenFromHeader(request: Request) {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
```

#### 2.3 Create Role-Based Guards
```typescript
// src/guards/roles.guard.ts
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<UserRole[]>('roles', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.role === role);
  }
}

// src/decorators/roles.decorator.ts
export const Roles = (...roles: UserRole[]) => SetMetadata('roles', roles);

// Usage in controllers:
@UseGuards(UnifiedAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
@Get('/admin/users')
async getUsers() {
  // Only admins and super admins can access
}
```

### Phase 3: API Endpoint Consolidation

#### 3.1 Unified Authentication Endpoints
```typescript
// src/auth/auth.controller.ts
@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private authService: UnifiedAuthService) {}

  @Post('/login')
  @ApiOperation({
    summary: 'Unified login for all user types',
    description: 'Single login endpoint that handles users, admins, and mentors',
  })
  async login(@Body() dto: LoginDto) {
    return await this.authService.login(dto);
  }

  @Post('/register')
  @ApiOperation({
    summary: 'Register new user',
    description: 'Register a new user with default USER role',
  })
  async register(@Body() dto: CreateUserDto) {
    return await this.authService.register(dto, UserRole.USER);
  }

  @UseGuards(UnifiedAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @Post('/register/mentor')
  @ApiOperation({
    summary: 'Register new mentor (Admin only)',
    description: 'Admin endpoint to register a new mentor',
  })
  async registerMentor(@Body() dto: CreateMentorDto) {
    return await this.authService.register(dto, UserRole.MENTOR);
  }

  @UseGuards(UnifiedAuthGuard, RolesGuard)
  @Roles(UserRole.SUPER_ADMIN)
  @Post('/register/admin')
  @ApiOperation({
    summary: 'Register new admin (Super Admin only)',
    description: 'Super admin endpoint to register a new admin',
  })
  async registerAdmin(@Body() dto: CreateUserDto) {
    return await this.authService.register(dto, UserRole.ADMIN);
  }
}
```

#### 3.2 Unified User Management
```typescript
// src/users/users.controller.ts
@ApiTags('users')
@Controller('users')
export class UsersController {
  constructor(private usersService: UnifiedUsersService) {}

  @UseGuards(UnifiedAuthGuard)
  @Get('/profile')
  @ApiOperation({
    summary: 'Get current user profile',
    description: 'Get authenticated user profile regardless of role',
  })
  async getProfile(@GetUser() user: IJWTPayload) {
    return await this.usersService.getProfile(user.id);
  }

  @UseGuards(UnifiedAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @Get('/admin/all')
  @ApiOperation({
    summary: 'Get all users (Admin only)',
    description: 'Get paginated list of all users with role filtering',
  })
  async getAllUsers(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('role') role?: UserRole,
    @Query('status') status?: UserStatus,
  ) {
    return await this.usersService.getAllUsers({ page, limit, role, status });
  }

  @UseGuards(UnifiedAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @Patch('/admin/:userId/role')
  @ApiOperation({
    summary: 'Update user role (Admin only)',
    description: 'Change user role and permissions',
  })
  async updateUserRole(
    @Param('userId') userId: string,
    @Body() dto: UpdateRoleDto,
  ) {
    return await this.usersService.updateUserRole(userId, dto);
  }

  @UseGuards(UnifiedAuthGuard, RolesGuard)
  @Roles(UserRole.MENTOR)
  @Get('/mentor/dashboard')
  @ApiOperation({
    summary: 'Get mentor dashboard (Mentor only)',
    description: 'Get mentor-specific dashboard data',
  })
  async getMentorDashboard(@GetUser() user: IJWTPayload) {
    return await this.usersService.getMentorDashboard(user.id);
  }

  @UseGuards(UnifiedAuthGuard, RolesGuard)
  @Roles(UserRole.AGENT)
  @Get('/agent/dashboard')
  @ApiOperation({
    summary: 'Get agent dashboard (Agent only)',
    description: 'Get agent-specific dashboard with case management data',
  })
  async getAgentDashboard(@GetUser() user: IJWTPayload) {
    return await this.usersService.getAgentDashboard(user.id);
  }

  @UseGuards(UnifiedAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @Get('/admin/agents')
  @ApiOperation({
    summary: 'Get all agents (Admin only)',
    description: 'Get paginated list of all immigration agents',
  })
  async getAllAgents(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('status') status?: UserStatus,
  ) {
    return await this.usersService.getAllUsers({
      page,
      limit,
      role: UserRole.AGENT,
      status
    });
  }
}
```

### Phase 4: Database Relationship Updates

#### 4.1 Update Foreign Key Relationships
```sql
-- Update review table to use unified_user
ALTER TABLE "review" DROP CONSTRAINT "review_userId_fkey";
ALTER TABLE "review" DROP CONSTRAINT "review_mentorId_fkey";

ALTER TABLE "review" ADD CONSTRAINT "review_userId_fkey"
    FOREIGN KEY ("userId") REFERENCES "unified_user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "review" ADD CONSTRAINT "review_mentorId_fkey"
    FOREIGN KEY ("mentorId") REFERENCES "unified_user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Update service table
ALTER TABLE "service" DROP CONSTRAINT "service_mentorId_fkey";
ALTER TABLE "service" ADD CONSTRAINT "service_mentorId_fkey"
    FOREIGN KEY ("mentorId") REFERENCES "unified_user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Update comment table
ALTER TABLE "comment" DROP CONSTRAINT "comment_authorId_fkey";
ALTER TABLE "comment" ADD CONSTRAINT "comment_authorId_fkey"
    FOREIGN KEY ("authorId") REFERENCES "unified_user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Update payment table (if using unified payment table)
ALTER TABLE "payment" DROP CONSTRAINT "payment_userId_fkey";
ALTER TABLE "payment" ADD CONSTRAINT "payment_userId_fkey"
    FOREIGN KEY ("userId") REFERENCES "unified_user"("id") ON DELETE SET NULL ON UPDATE CASCADE;
```

#### 4.2 Create User Role History Table
```sql
CREATE TABLE "user_role_history" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "old_role" TEXT,
    "new_role" TEXT NOT NULL,
    "changed_by" TEXT NOT NULL,
    "reason" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_role_history_pkey" PRIMARY KEY ("id")
);

ALTER TABLE "user_role_history" ADD CONSTRAINT "user_role_history_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES "unified_user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "user_role_history" ADD CONSTRAINT "user_role_history_changed_by_fkey"
    FOREIGN KEY ("changed_by") REFERENCES "unified_user"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

CREATE INDEX "user_role_history_user_id_idx" ON "user_role_history"("user_id");
CREATE INDEX "user_role_history_created_at_idx" ON "user_role_history"("created_at");
```

## API Endpoint Impact Analysis

### Affected Endpoints Overview

The user role migration will impact **30+ API endpoints** across **4 controllers** that currently handle separate user types, plus new agent-specific endpoints that will be created.

### 🔴 **Critical Impact Endpoints**

#### Authentication Endpoints
1. **POST `/user/login`** → **POST `/auth/login`**
   - **Current**: Separate login for users
   - **After Migration**: Unified login for all roles
   - **Impact**: Complete endpoint consolidation

2. **POST `/admin/login`** → **POST `/auth/login`**
   - **Current**: Separate admin login
   - **After Migration**: Same unified login endpoint
   - **Impact**: Endpoint removal, logic consolidation

3. **POST `/mentor/login`** → **POST `/auth/login`**
   - **Current**: Separate mentor login
   - **After Migration**: Same unified login endpoint
   - **Impact**: Endpoint removal, logic consolidation

4. **POST `/user/register`** → **POST `/auth/register`**
   - **Current**: User registration only
   - **After Migration**: Role-based registration
   - **Impact**: Enhanced with role parameter

#### User Management Endpoints
5. **GET `/user`** → **GET `/users/profile`**
   - **Current**: Get user profile
   - **After Migration**: Get profile for any role
   - **Impact**: Response format includes role information

6. **GET `/user/admin`** → **GET `/users/admin/all`**
   - **Current**: Admin gets all users
   - **After Migration**: Admin gets all users with role filtering
   - **Impact**: Enhanced filtering capabilities

7. **POST `/user/admin`** → **POST `/auth/register`** (with role)
   - **Current**: Admin creates user
   - **After Migration**: Admin creates user with specific role
   - **Impact**: Role-based user creation

#### Mentor Management Endpoints
8. **GET `/mentor`** → **GET `/users/admin/all?role=mentor`**
   - **Current**: Get all mentors
   - **After Migration**: Get users filtered by mentor role
   - **Impact**: Query parameter change

9. **POST `/mentor/admin`** → **POST `/auth/register/mentor`**
   - **Current**: Admin creates mentor
   - **After Migration**: Admin creates user with mentor role
   - **Impact**: Simplified mentor creation

#### Agent Management Endpoints (New)
10. **POST `/auth/register/agent`** (New endpoint)
    - **Current**: No agent system exists
    - **After Migration**: Admin creates user with agent role
    - **Impact**: New agent registration capability

11. **GET `/users/admin/agents`** (New endpoint)
    - **Current**: No agent management
    - **After Migration**: Admin gets all agents with filtering
    - **Impact**: New agent management interface

12. **GET `/users/agent/dashboard`** (New endpoint)
    - **Current**: No agent dashboard
    - **After Migration**: Agent-specific dashboard with case management
    - **Impact**: New agent functionality

### 🟡 **Medium Impact Endpoints**

#### Profile Management
13. **PATCH `/user`** → **PATCH `/users/profile`**
    - **Current**: Update user profile
    - **After Migration**: Update profile with role-specific fields
    - **Impact**: Enhanced with mentor-specific and agent-specific fields

14. **DELETE `/user`** → **DELETE `/users/profile`**
    - **Current**: Delete user account
    - **After Migration**: Soft delete with status change
    - **Impact**: Soft delete implementation

#### Dashboard Endpoints
15. **GET `/dashboard`** → **GET `/users/admin/dashboard`**
    - **Current**: Admin dashboard
    - **After Migration**: Role-based dashboard
    - **Impact**: Role-specific dashboard data

### Response Format Changes

#### Current Response Format (User Login)
```json
{
  "user": {
    "id": "user123",
    "name": "John Doe",
    "email": "<EMAIL>",
    "provider": "credentials"
  },
  "backendTokens": {
    "accessToken": "jwt_token",
    "refreshToken": "refresh_token"
  }
}
```

#### New Response Format (Unified Login)

**User Login:**
```json
{
  "user": {
    "id": "user123",
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "user",
    "status": "active",
    "provider": "credentials",
    "permissions": ["purchase_services", "leave_reviews"],
    "last_login": "2024-01-01T00:00:00Z"
  },
  "backendTokens": {
    "accessToken": "jwt_token",
    "refreshToken": "refresh_token"
  }
}
```

**Agent Login:**
```json
{
  "user": {
    "id": "agent123",
    "name": "Sarah O'Connor",
    "email": "<EMAIL>",
    "role": "agent",
    "status": "active",
    "provider": "credentials",
    "license_number": "ICCRC-2024-001",
    "specializations": ["Family Reunification", "Work Permits"],
    "years_experience": 8,
    "consultation_fee": 15000,
    "languages": ["English", "Irish", "French"],
    "office_address": "Dublin 2, Ireland",
    "certification_body": "ICCRC",
    "license_expiry": "2025-12-31",
    "permissions": ["manage_immigration_services", "view_client_cases"],
    "last_login": "2024-01-01T00:00:00Z"
  },
  "backendTokens": {
    "accessToken": "jwt_token",
    "refreshToken": "refresh_token"
  }
}
```

### Backward Compatibility Strategy

#### Option 1: API Versioning (Recommended)
```typescript
// v1 endpoints (legacy) - maintain for transition period
@Controller('v1/user')
export class UserV1Controller {
  // Legacy user endpoints with transformation layer
}

@Controller('v1/admin')
export class AdminV1Controller {
  // Legacy admin endpoints with transformation layer
}

@Controller('v1/mentor')
export class MentorV1Controller {
  // Legacy mentor endpoints with transformation layer
}

// v2 endpoints (new unified)
@Controller('v2/auth')
export class AuthV2Controller {
  // New unified authentication
}

@Controller('v2/users')
export class UsersV2Controller {
  // New unified user management
}
```

#### Option 2: Gradual Migration with Feature Flags
```typescript
@Injectable()
export class UserService {
  async login(dto: LoginDto) {
    if (this.configService.get('USE_UNIFIED_AUTH')) {
      return this.unifiedAuthService.login(dto);
    } else {
      return this.legacyUserService.login(dto);
    }
  }
}
```

## Production Impact Assessment

### 🔴 **High Impact Areas**

#### 1. **Authentication System Changes**
- **JWT Token Structure**: New payload format with role and permissions
- **Guard System**: Unified guards replacing 3 separate guards
- **Session Management**: Single session handling for all user types

#### 2. **Database Schema Changes**
- **Table Consolidation**: 3 tables → 1 unified table
- **Foreign Key Updates**: All user references need updating
- **Data Migration**: Complex migration of user data with role assignment

#### 3. **Authorization Logic**
- **Role-Based Access**: New permission system implementation
- **Guard Updates**: All endpoints need guard updates
- **Permission Checks**: Granular permission validation

### 🟡 **Medium Impact Areas**

#### 1. **API Response Formats**
- **User Objects**: Enhanced with role and permission information
- **Authentication Responses**: New token payload structure
- **Profile Data**: Unified profile with role-specific fields

#### 2. **Frontend Applications**
- **Login Flows**: Single login form for all user types
- **Role Detection**: Frontend role-based UI rendering
- **Permission Handling**: Client-side permission checks

#### 3. **Admin Interfaces**
- **User Management**: Unified user management interface
- **Role Assignment**: New role management features
- **Permission Management**: Granular permission controls

### 🟢 **Low Impact Areas**

#### 1. **Business Logic**
- **Payment Processing**: No changes to payment flows
- **Service Management**: Minimal changes to service logic
- **Email Notifications**: Same notification system

#### 2. **External Integrations**
- **Stripe Webhooks**: No changes required
- **OAuth Providers**: Same OAuth integration
- **Email Services**: No changes to email sending

## Migration Timeline

### **Phase 1: Preparation (2-3 weeks)**
- [ ] Design unified user schema
- [ ] Create migration scripts
- [ ] Implement unified auth service
- [ ] Update Prisma schema
- [ ] Create role-based guards

### **Phase 2: Testing (1-2 weeks)**
- [ ] Staging environment migration
- [ ] API endpoint testing
- [ ] Authentication flow testing
- [ ] Permission system validation
- [ ] Performance testing

### **Phase 3: Production Migration (1 day)**
- [ ] **Hour 0-2**: Create unified user table (0 downtime)
- [ ] **Hour 2-4**: Migrate user data (0 downtime)
- [ ] **Hour 4-6**: Update foreign key relationships (minimal downtime)
- [ ] **Hour 6-8**: Switch to unified authentication (2-hour downtime)
- [ ] **Hour 8-10**: Validation and monitoring (0 downtime)

### **Phase 4: Cleanup (1-2 weeks)**
- [ ] Remove old user tables
- [ ] Clean up legacy endpoints
- [ ] Update documentation
- [ ] Performance optimization
- [ ] Security audit

## Success Criteria

### **Data Integrity**
- [ ] 100% user data preservation during migration
- [ ] All user relationships maintained correctly
- [ ] Role assignments accurate for all users
- [ ] Permission mappings correct

### **Functionality**
- [ ] All authentication flows working
- [ ] Role-based access control functioning
- [ ] Permission system operational
- [ ] User management features working

### **Performance**
- [ ] Login performance maintained or improved
- [ ] Database query performance optimized
- [ ] API response times within acceptable limits
- [ ] System scalability improved

### **Security**
- [ ] No security vulnerabilities introduced
- [ ] Role-based access properly enforced
- [ ] Permission system secure
- [ ] Audit trail functioning

This migration will significantly improve the user management system by providing a unified, scalable, and secure approach to handling different user types while maintaining backward compatibility during the transition period.
```
